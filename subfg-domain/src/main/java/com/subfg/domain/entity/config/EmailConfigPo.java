package com.subfg.domain.entity.config;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("config_email")
public class EmailConfigPo {

    @TableField("id")
    private Integer id;

    @TableField("host")
    private String host;

    @TableField("account")
    private String account;

    @TableField("password")
    private String password;

    @TableField("port")
    private Integer port;

    @TableField("protocol")
    private String protocol;
}
