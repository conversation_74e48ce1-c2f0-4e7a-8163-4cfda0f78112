package com.subfg.common.util;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * 时间工具类
 * 提供时间生成、格式化、转换等功能
 */
public class TimeUtil {

    // 常用时间格式
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String TIME_FORMAT = "HH:mm:ss";
    public static final String DATETIME_FORMAT_COMPACT = "yyyyMMddHHmmss";
    public static final String DATE_FORMAT_COMPACT = "yyyyMMdd";
    public static final String DATETIME_FORMAT_MILLISECOND = "yyyy-MM-dd HH:mm:ss.SSS";

    // 常用时间格式化器
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(DATETIME_FORMAT);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMAT);
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(TIME_FORMAT);
    private static final DateTimeFormatter DATETIME_FORMATTER_COMPACT = DateTimeFormatter.ofPattern(DATETIME_FORMAT_COMPACT);
    private static final DateTimeFormatter DATE_FORMATTER_COMPACT = DateTimeFormatter.ofPattern(DATE_FORMAT_COMPACT);
    private static final DateTimeFormatter DATETIME_FORMATTER_MILLISECOND = DateTimeFormatter.ofPattern(DATETIME_FORMAT_MILLISECOND);

    /**
     * 获取当前时间戳（秒）
     */
    public static long getCurrentTimestamp() {
        return Instant.now().getEpochSecond();
    }

    /**
     * 获取当前时间戳（毫秒）
     */
    public static long getCurrentTimestampMillis() {
        return System.currentTimeMillis();
    }

    /**
     * 获取当前时间戳（纳秒）
     */
    public static long getCurrentTimestampNanos() {
        return System.nanoTime();
    }

    /**
     * 获取当前日期时间字符串
     */
    public static String getCurrentDateTime() {
        return LocalDateTime.now().format(DATETIME_FORMATTER);
    }

    /**
     * 获取当前日期字符串
     */
    public static String getCurrentDate() {
        return LocalDate.now().format(DATE_FORMATTER);
    }

    /**
     * 获取当前时间字符串
     */
    public static String getCurrentTime() {
        return LocalTime.now().format(TIME_FORMATTER);
    }

    /**
     * 获取当前日期时间字符串（紧凑格式）
     */
    public static String getCurrentDateTimeCompact() {
        return LocalDateTime.now().format(DATETIME_FORMATTER_COMPACT);
    }

    /**
     * 获取当前日期字符串（紧凑格式）
     */
    public static String getCurrentDateCompact() {
        return LocalDate.now().format(DATE_FORMATTER_COMPACT);
    }

    /**
     * 获取当前日期时间字符串（包含毫秒）
     */
    public static String getCurrentDateTimeWithMillis() {
        return LocalDateTime.now().format(DATETIME_FORMATTER_MILLISECOND);
    }

    /**
     * 时间戳转日期时间字符串
     */
    public static String timestampToDateTime(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault())
                .format(DATETIME_FORMATTER);
    }

    /**
     * 毫秒时间戳转日期时间字符串
     */
    public static String timestampMillisToDateTime(long timestampMillis) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestampMillis), ZoneId.systemDefault())
                .format(DATETIME_FORMATTER);
    }

    /**
     * 时间戳转日期字符串
     */
    public static String timestampToDate(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault())
                .format(DATE_FORMATTER);
    }

    /**
     * 日期时间字符串转时间戳
     */
    public static long dateTimeToTimestamp(String dateTime) {
        return LocalDateTime.parse(dateTime, DATETIME_FORMATTER)
                .atZone(ZoneId.systemDefault())
                .toEpochSecond();
    }

    /**
     * 日期时间字符串转毫秒时间戳
     */
    public static long dateTimeToTimestampMillis(String dateTime) {
        return LocalDateTime.parse(dateTime, DATETIME_FORMATTER)
                .atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();
    }

    /**
     * 自定义格式化时间
     */
    public static String formatDateTime(LocalDateTime dateTime, String pattern) {
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 自定义格式化当前时间
     */
    public static String formatCurrentDateTime(String pattern) {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 解析时间字符串
     */
    public static LocalDateTime parseDateTime(String dateTime, String pattern) {
        return LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 获取今天开始时间戳（00:00:00）
     */
    public static long getTodayStartTimestamp() {
        return LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 获取今天结束时间戳（23:59:59）
     */
    public static long getTodayEndTimestamp() {
        return LocalDate.now().atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 获取指定天数后的时间戳
     */
    public static long getTimestampAfterDays(int days) {
        return LocalDateTime.now().plusDays(days).atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 获取指定小时后的时间戳
     */
    public static long getTimestampAfterHours(int hours) {
        return LocalDateTime.now().plusHours(hours).atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 获取指定分钟后的时间戳
     */
    public static long getTimestampAfterMinutes(int minutes) {
        return LocalDateTime.now().plusMinutes(minutes).atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 计算两个时间戳之间的天数差
     */
    public static long daysBetween(long timestamp1, long timestamp2) {
        LocalDate date1 = Instant.ofEpochSecond(timestamp1).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate date2 = Instant.ofEpochSecond(timestamp2).atZone(ZoneId.systemDefault()).toLocalDate();
        return ChronoUnit.DAYS.between(date1, date2);
    }

    /**
     * 计算两个时间戳之间的小时差
     */
    public static long hoursBetween(long timestamp1, long timestamp2) {
        return Math.abs(timestamp2 - timestamp1) / 3600;
    }

    /**
     * 计算两个时间戳之间的分钟差
     */
    public static long minutesBetween(long timestamp1, long timestamp2) {
        return Math.abs(timestamp2 - timestamp1) / 60;
    }

    /**
     * 判断是否是今天
     */
    public static boolean isToday(long timestamp) {
        LocalDate date = Instant.ofEpochSecond(timestamp).atZone(ZoneId.systemDefault()).toLocalDate();
        return date.equals(LocalDate.now());
    }

    /**
     * 判断是否是昨天
     */
    public static boolean isYesterday(long timestamp) {
        LocalDate date = Instant.ofEpochSecond(timestamp).atZone(ZoneId.systemDefault()).toLocalDate();
        return date.equals(LocalDate.now().minusDays(1));
    }

    /**
     * 判断时间戳是否过期
     */
    public static boolean isExpired(long timestamp) {
        return timestamp < getCurrentTimestamp();
    }

    /**
     * Date转时间戳
     */
    public static long dateToTimestamp(Date date) {
        return date.getTime() / 1000;
    }

    /**
     * 时间戳转Date
     */
    public static Date timestampToDate(long timestamp) {
        return new Date(timestamp * 1000);
    }

    /**
     * 获取本周开始时间戳（周一00:00:00）
     */
    public static long getWeekStartTimestamp() {
        LocalDate today = LocalDate.now();
        LocalDate monday = today.with(DayOfWeek.MONDAY);
        return monday.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 获取本月开始时间戳（1号00:00:00）
     */
    public static long getMonthStartTimestamp() {
        LocalDate today = LocalDate.now();
        LocalDate firstDay = today.withDayOfMonth(1);
        return firstDay.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 获取本年开始时间戳（1月1号00:00:00）
     */
    public static long getYearStartTimestamp() {
        LocalDate today = LocalDate.now();
        LocalDate firstDay = today.withDayOfYear(1);
        return firstDay.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 获取友好的时间描述（如：刚刚、1分钟前、1小时前等）
     */
    public static String getFriendlyTime(long timestamp) {
        long now = getCurrentTimestamp();
        long diff = now - timestamp;

        if (diff < 60) {
            return "刚刚";
        } else if (diff < 3600) {
            return (diff / 60) + "分钟前";
        } else if (diff < 86400) {
            return (diff / 3600) + "小时前";
        } else if (diff < 2592000) {
            return (diff / 86400) + "天前";
        } else if (diff < 31536000) {
            return (diff / 2592000) + "个月前";
        } else {
            return (diff / 31536000) + "年前";
        }
    }
}
